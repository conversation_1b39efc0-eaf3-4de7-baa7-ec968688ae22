MATCH (source:<PERSON><PERSON><PERSON><PERSON><PERSON> {NAME: '_GET'})<-[*1..100]-(func_call:CALL)
                          MATCH (sink:CALL {NAME: 'system'})<-[*1..200]-(method:METHOD)
                          RETURN 
                            source.LINE_NUMBER as source_line,
                            source.CODE as source_code,
                            func_call.METHOD_FULL_NAME as intermediate_function,
                            func_call.LINE_NUMBER as call_line,
                            func_call.CODE as call_code,
                            sink.LINE_NUMBER as sink_line,
                            sink.CODE as sink_code,
                            method.FULL_NAME as sink_method,
                            2 as path_length,
                            'GET->FUNCTION_CALL->SYSTEM' as path_description
                          ORDER BY source_line, intermediate_function, sink_line;