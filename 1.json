{"detailed_flow_chain": [{"file": "unknown", "method": "<global>", "line": 3, "name": "_GET", "tracked": "$_GET", "nodeType": "Identifier", "nodeId": 43, "order": 1}, {"file": "index.php", "method": "<global>", "line": 3, "name": "cmd", "tracked": "$cmd", "nodeType": "Identifier", "nodeId": 42, "order": 1}, {"file": "unknown", "method": "<global>", "line": 3, "name": "_GET", "tracked": "$_GET", "nodeType": "Identifier", "nodeId": 44, "order": 1}, {"file": "index.php", "method": "<global>", "line": 3, "name": "<operator>.assignment", "tracked": "$cmd = isset($_GET[\"cmd\"]) ? $_GET[\"cmd\"] : \"\"", "nodeType": "Call", "nodeId": 26, "order": 4}, {"file": "index.php", "method": "<global>", "line": 3, "name": "<operator>.conditional", "tracked": "isset($_GET[\"cmd\"]) ? $_GET[\"cmd\"] : \"\"", "nodeType": "Call", "nodeId": 27, "order": 2}, {"file": "unknown", "method": "<global>", "line": 3, "name": "isset", "tracked": "isset($_GET[\"cmd\"])", "nodeType": "Call", "nodeId": 28, "order": 1}, {"file": "unknown", "method": "<global>", "line": 3, "name": "<operator>.indexAccess", "tracked": "$_GET[\"cmd\"]", "nodeType": "Call", "nodeId": 29, "order": 1}, {"file": "unknown", "method": "<global>", "line": 3, "name": "<operator>.indexAccess", "tracked": "$_GET[\"cmd\"]", "nodeType": "Call", "nodeId": 30, "order": 2}, {"file": "func2.php", "method": "func2", "line": 2, "name": "c2", "tracked": "$c2", "nodeType": "MethodParameterIn", "nodeId": 65, "order": 1}, {"file": "func2.php", "method": "func2", "line": 6, "name": "func3", "tracked": "func3($c2)", "nodeType": "Call", "nodeId": 22, "order": 2}, {"file": "func2.php", "method": "func2", "line": 6, "name": "c2", "tracked": "$c2", "nodeType": "Identifier", "nodeId": 40, "order": 1}, {"file": "func2.php", "method": "func3", "line": 10, "name": "hhh", "tracked": "$hhh", "nodeType": "Identifier", "nodeId": 41, "order": 1}, {"file": "func2.php", "method": "func3", "line": 10, "name": "system", "tracked": "system($hhh)", "nodeType": "Call", "nodeId": 24, "order": 2}]}