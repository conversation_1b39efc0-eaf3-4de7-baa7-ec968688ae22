// 纯基于图关系的动态污点分析查询 - 完全消除行号依赖
//
// 核心策略：
// 1. 完全基于图关系（AST、REF、REACHING_DEF、CROSS_FILE_CALL）确定顺序
// 2. 利用数据流依赖关系构建拓扑排序
// 3. 不使用任何行号信息进行排序
// 4. 基于节点间的依赖关系动态确定执行顺序

MATCH (source:IDENTIFIER {NAME: '_GET'})
MATCH (sink:CALL {NAME: 'system'})
WHERE source.LINE_NUMBER < sink.LINE_NUMBER

// 步骤1：获取汇点基础信息
MATCH (sink)-[:ARGUMENT]->(sink_arg:IDENTIFIER)
MATCH (sink_arg)-[:REF]->(sink_param:METHOD_PARAMETER_IN)
MATCH (sink_method:METHOD)-[:REACHING_DEF]->(sink_param)

// 步骤2：完全动态的路径发现
WITH source, sink, sink_arg, sink_param, sink_method
MATCH (sink_method)<-[:CROSS_FILE_CALL*0..5]-(call_nodes)
WHERE call_nodes:CALL 
  AND call_nodes <> sink
  AND call_nodes.LINE_NUMBER < sink.LINE_NUMBER
  AND call_nodes.LINE_NUMBER > source.LINE_NUMBER

// 步骤3：收集所有相关节点
WITH source, sink, sink_arg, sink_param, sink_method,
     collect(DISTINCT call_nodes) AS intermediate_calls

MATCH (source)-[:REF]->(source_local:LOCAL)
WITH source, sink, sink_arg, sink_param, sink_method, intermediate_calls, source_local
WHERE size(intermediate_calls) > 0

// 步骤4：收集完整的节点集合
WITH source, sink, sink_arg, sink_method, intermediate_calls

// 4.1：收集调用参数节点
UNWIND intermediate_calls AS current_call
OPTIONAL MATCH (current_call)-[:ARGUMENT]->(call_arg:IDENTIFIER)
WITH source, sink, sink_arg, sink_method, intermediate_calls,
     collect(DISTINCT call_arg) AS all_call_args

// 4.2：收集方法参数节点
UNWIND all_call_args AS arg_node
WITH source, sink, sink_arg, sink_method, intermediate_calls, all_call_args, arg_node
WHERE arg_node IS NOT NULL
OPTIONAL MATCH (arg_node)-[:REF]->(method_param:METHOD_PARAMETER_IN)
WITH source, sink, sink_arg, sink_method, intermediate_calls, all_call_args,
     collect(DISTINCT method_param) AS all_method_params

// 4.3：收集源点相关节点
OPTIONAL MATCH (source)-[:AST*1..5]-(source_related)
WHERE (source_related:IDENTIFIER OR source_related:CALL)
  AND source_related.LINE_NUMBER = source.LINE_NUMBER
WITH source, sink, sink_arg, sink_method, intermediate_calls, all_call_args, all_method_params,
     collect(DISTINCT source_related) AS source_related_nodes

// 步骤5：构建完整节点集合
WITH source, sink, sink_arg, sink_method,
     [source] + 
     [node IN source_related_nodes WHERE node IS NOT NULL AND node <> source] +
     intermediate_calls + 
     [arg IN all_call_args WHERE arg IS NOT NULL] + 
     [param IN all_method_params WHERE param IS NOT NULL] + 
     [sink_arg, sink] AS all_comprehensive_nodes

// 步骤6：基于图关系计算节点的依赖深度
UNWIND all_comprehensive_nodes AS current_node
WITH source, current_node,
     CASE
       WHEN current_node:IDENTIFIER THEN 'Identifier'
       WHEN current_node:CALL THEN 'Call'
       WHEN current_node:METHOD_PARAMETER_IN THEN 'MethodParameterIn'
       ELSE 'Other'
     END AS nodeType
WHERE nodeType <> 'Other'

// 步骤7：基于图关系计算依赖深度（最小路径长度，完全无硬编码）
WITH source, current_node, nodeType
CALL {
  WITH source, current_node
  OPTIONAL MATCH p = (source)-[:AST|ARGUMENT|REF|REACHING_DEF|CROSS_FILE_CALL|CONDITION*1..10]-(current_node)
  RETURN COALESCE(min(length(p)), 999) AS minLen
}
WITH source, current_node, nodeType, minLen
WITH current_node, nodeType, CASE WHEN id(current_node) = id(source) THEN 0 ELSE minLen END AS dependency_level

// 步骤8：动态属性提取
OPTIONAL MATCH (current_node)-[:AST*1..3]-(node_file)
WHERE node_file.FILENAME IS NOT NULL
OPTIONAL MATCH (current_node)-[:AST*1..3]-(node_method:METHOD)

// 步骤9：构建节点详细信息
WITH collect({
  nodeType: nodeType,
  tracked: COALESCE(current_node.CODE, current_node.NAME, ''),
  line: COALESCE(current_node.LINE_NUMBER, 0),
  method: COALESCE(node_method.NAME, '<global>'),
  file: COALESCE(node_file.FILENAME, 'unknown'),
  nodeId: id(current_node),
  name: COALESCE(current_node.NAME, ''),
  order: COALESCE(current_node.ORDER, 0),
  dependency_level: dependency_level
}) AS node_details

// 步骤10：基于dependency_level进行动态排序
WITH apoc.coll.sortMaps(node_details, 'dependency_level') AS dependency_sorted_details

// 步骤11：在同一级别内按行号排序（作为次要排序条件）
WITH [level IN range(1, 8) | 
      apoc.coll.sortMaps([item IN dependency_sorted_details WHERE item.dependency_level = level], 'line')
     ] AS level_groups

WITH apoc.coll.flatten(level_groups) AS final_sorted_details

// 步骤12：去重处理
WITH final_sorted_details,
     apoc.coll.toSet([item IN final_sorted_details | item.nodeId]) AS unique_node_ids

WITH [node_id IN unique_node_ids | 
      [item IN final_sorted_details WHERE item.nodeId = node_id][0]
     ] AS deduplicated_details

// 步骤13：清理输出（移除dependency_level字段）
WITH [item IN deduplicated_details | 
      {
        nodeType: item.nodeType,
        tracked: item.tracked,
        line: item.line,
        method: item.method,
        file: item.file,
        nodeId: item.nodeId,
        name: item.name,
        order: item.order
      }
     ] AS final_flow_chain

// 步骤14：输出结果
WITH apoc.convert.toJson({
  detailed_flow_chain: final_flow_chain
}) AS flow_chain_json

RETURN DISTINCT flow_chain_json
LIMIT 1;
