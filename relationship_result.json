{"detailed_flow_chain": [{"file": "func2.php", "method": "func3", "line": 10, "name": "hhh", "tracked": "$hhh", "nodeType": "Identifier", "nodeId": 41, "order": 1}, {"file": "func2.php", "method": "func3", "line": 10, "name": "system", "tracked": "system($hhh)", "nodeType": "Call", "nodeId": 24, "order": 2}, {"file": "func2.php", "method": "func2", "line": 6, "name": "func3", "tracked": "func3($c2)", "nodeType": "Call", "nodeId": 22, "order": 2}, {"file": "func2.php", "method": "func2", "line": 6, "name": "c2", "tracked": "$c2", "nodeType": "Identifier", "nodeId": 40, "order": 1}, {"file": "unknown", "method": "<global>", "line": 3, "name": "_GET", "tracked": "$_GET", "nodeType": "Identifier", "nodeId": 43, "order": 1}, {"file": "unknown", "method": "<global>", "line": 3, "name": "_GET", "tracked": "$_GET", "nodeType": "Identifier", "nodeId": 44, "order": 1}, {"file": "func2.php", "method": "func2", "line": 2, "name": "c2", "tracked": "$c2", "nodeType": "MethodParameterIn", "nodeId": 65, "order": 1}]}