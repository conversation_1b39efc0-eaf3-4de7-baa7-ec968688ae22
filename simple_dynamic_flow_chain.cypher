// 简化的完全动态污点分析查询 - 方案B
//
// 特点：
// 1. 完全消除硬编码（包括行号分组）
// 2. 不包含复杂的中间节点收集逻辑
// 3. 基于简单的行号排序
// 4. 执行性能更高，逻辑更简单
// 5. 保持完全动态的路径发现机制

MATCH (source:IDENTIFIER {NAME: '_GET'})
MATCH (sink:CALL {NAME: 'system'})
WHERE source.LINE_NUMBER < sink.LINE_NUMBER

// 步骤1：获取汇点基础信息
MATCH (sink)-[:ARGUMENT]->(sink_arg:IDENTIFIER)
MATCH (sink_arg)-[:REF]->(sink_param:METHOD_PARAMETER_IN)
MATCH (sink_method:METHOD)-[:REACHING_DEF]->(sink_param)

// 步骤2：完全动态的路径发现
WITH source, sink, sink_arg, sink_param, sink_method
MATCH (sink_method)<-[:CROSS_FILE_CALL*0..5]-(call_nodes)
WHERE call_nodes:CALL 
  AND call_nodes <> sink
  AND call_nodes.LINE_NUMBER < sink.LINE_NUMBER
  AND call_nodes.LINE_NUMBER > source.LINE_NUMBER

// 步骤3：收集所有中间调用
WITH source, sink, sink_arg, sink_param, sink_method,
     collect(DISTINCT call_nodes) AS intermediate_calls

// 步骤4：数据流验证
MATCH (source)-[:REF]->(source_local:LOCAL)
WITH source, sink, sink_arg, sink_param, sink_method, intermediate_calls, source_local
WHERE size(intermediate_calls) > 0

// 步骤5：收集基本的相关节点（简化版本）
WITH source, sink, sink_arg, sink_method, intermediate_calls

// 5.1：收集调用参数节点
UNWIND intermediate_calls AS current_call
OPTIONAL MATCH (current_call)-[:ARGUMENT]->(call_arg:IDENTIFIER)
WITH source, sink, sink_arg, sink_method, intermediate_calls,
     collect(DISTINCT call_arg) AS all_call_args

// 5.2：收集方法参数节点
UNWIND all_call_args AS arg_node
WITH source, sink, sink_arg, sink_method, intermediate_calls, all_call_args, arg_node
WHERE arg_node IS NOT NULL
OPTIONAL MATCH (arg_node)-[:REF]->(method_param:METHOD_PARAMETER_IN)
WITH source, sink, sink_arg, sink_method, intermediate_calls, all_call_args,
     collect(DISTINCT method_param) AS all_method_params

// 步骤6：构建基本的节点序列（简化版本）
WITH source, sink, sink_arg, sink_method,
     // 基本节点序列：源点 -> 中间调用 -> 参数 -> 汇点
     [source] + 
     intermediate_calls + 
     [arg IN all_call_args WHERE arg IS NOT NULL] + 
     [param IN all_method_params WHERE param IS NOT NULL] + 
     [sink_arg, sink] AS basic_nodes

// 步骤7：展开节点并分类
UNWIND basic_nodes AS current_node
WITH current_node,
     CASE 
       WHEN current_node:IDENTIFIER THEN 'Identifier'
       WHEN current_node:CALL THEN 'Call'
       WHEN current_node:METHOD_PARAMETER_IN THEN 'MethodParameterIn'
       ELSE 'Other'
     END AS nodeType
WHERE nodeType <> 'Other'

// 步骤8：为每个节点动态提取属性信息
OPTIONAL MATCH (current_node)-[:AST*1..3]-(node_file)
WHERE node_file.FILENAME IS NOT NULL
OPTIONAL MATCH (current_node)-[:AST*1..3]-(node_method:METHOD)

// 步骤9：构建节点详细信息
WITH collect({
  nodeType: nodeType,
  tracked: COALESCE(current_node.CODE, current_node.NAME, ''),
  line: COALESCE(current_node.LINE_NUMBER, 0),
  method: COALESCE(node_method.NAME, '<global>'),
  file: COALESCE(node_file.FILENAME, 'unknown'),
  nodeId: id(current_node),
  name: COALESCE(current_node.NAME, ''),
  order: COALESCE(current_node.ORDER, 0)
}) AS basic_details

// 步骤10：简单的行号排序（完全动态，无硬编码）
WITH apoc.coll.sortMaps(basic_details, 'line') AS sorted_details

// 步骤11：基于nodeId去重
WITH sorted_details,
     apoc.coll.toSet([item IN sorted_details | item.nodeId]) AS unique_node_ids

WITH [node_id IN unique_node_ids | 
      [item IN sorted_details WHERE item.nodeId = node_id][0]
     ] AS deduplicated_details

// 步骤12：保持简单的行号顺序
WITH deduplicated_details AS final_flow_chain

// 步骤13：只输出detailed_flow_chain字段
WITH apoc.convert.toJson({
  detailed_flow_chain: final_flow_chain
}) AS flow_chain_json

RETURN DISTINCT flow_chain_json
LIMIT 1;
