// 简化的完全动态污点分析查询 - 消除所有硬编码层级
//
// 核心策略：
// 1. 利用CROSS_FILE_CALL关系进行完全动态的路径发现
// 2. 不使用任何硬编码层级变量（call1、call2、call3等）
// 3. 简化数据流验证逻辑
// 4. 只输出detailed_flow_chain字段
// 5. 所有属性信息动态提取

MATCH (source:IDENTIFIER {NAME: '_GET'})
MATCH (sink:CALL {NAME: 'system'})
WHERE source.LINE_NUMBER < sink.LINE_NUMBER

// 步骤1：获取汇点基础信息
MATCH (sink)-[:ARGUMENT]->(sink_arg:IDENTIFIER)
MATCH (sink_arg)-[:REF]->(sink_param:METHOD_PARAMETER_IN)
MATCH (sink_method:METHOD)-[:REACHING_DEF]->(sink_param)

// 步骤2：完全动态的路径发现
// 使用可变长度路径匹配找到所有通过CROSS_FILE_CALL关系连接的调用
WITH source, sink, sink_arg, sink_param, sink_method
MATCH (sink_method)<-[:CROSS_FILE_CALL*0..5]-(call_nodes)
WHERE call_nodes:CALL 
  AND call_nodes <> sink
  AND call_nodes.LINE_NUMBER < sink.LINE_NUMBER
  AND call_nodes.LINE_NUMBER > source.LINE_NUMBER

// 步骤3：收集所有中间调用并去重
WITH source, sink, sink_arg, sink_param, sink_method,
     collect(DISTINCT call_nodes) AS intermediate_calls

// 步骤4：简化的数据流验证
// 验证至少存在一个调用链与源点有连接
MATCH (source)-[:REF]->(source_local:LOCAL)
WITH source, sink, sink_arg, sink_param, sink_method, intermediate_calls, source_local
WHERE size(intermediate_calls) > 0

// 步骤5：按行号排序中间调用
WITH source, sink, sink_arg, sink_method, source_local,
     [call IN intermediate_calls | call] AS ordered_calls

// 步骤6：全面收集所有相关节点 - 扩展AST遍历深度
WITH source, sink, sink_arg, sink_method, ordered_calls

// 6.1：收集所有调用参数节点
UNWIND ordered_calls AS current_call
OPTIONAL MATCH (current_call)-[:ARGUMENT]->(call_arg:IDENTIFIER)
WITH source, sink, sink_arg, sink_method, ordered_calls,
     collect(DISTINCT call_arg) AS all_call_args

// 6.2：收集所有方法参数节点
UNWIND all_call_args AS arg_node
WITH source, sink, sink_arg, sink_method, ordered_calls, all_call_args, arg_node
WHERE arg_node IS NOT NULL
OPTIONAL MATCH (arg_node)-[:REF]->(method_param:METHOD_PARAMETER_IN)
WITH source, sink, sink_arg, sink_method, ordered_calls, all_call_args,
     collect(DISTINCT method_param) AS all_method_params

// 6.3：收集方法内部的所有相关节点（扩展AST遍历）
UNWIND all_method_params AS param_node
WITH source, sink, sink_arg, sink_method, ordered_calls, all_call_args, all_method_params, param_node
WHERE param_node IS NOT NULL
OPTIONAL MATCH (param_method:METHOD)-[:REACHING_DEF]->(param_node)
OPTIONAL MATCH (param_method)-[:AST*1..10]-(internal_node)
WHERE (internal_node:IDENTIFIER OR internal_node:CALL)
  AND internal_node.LINE_NUMBER >= param_method.LINE_NUMBER_BEGIN
  AND internal_node.LINE_NUMBER <= param_method.LINE_NUMBER_END
  AND internal_node <> sink_arg
  AND NOT internal_node IN all_call_args
WITH source, sink, sink_arg, sink_method, ordered_calls, all_call_args, all_method_params,
     collect(DISTINCT internal_node) AS all_internal_nodes

// 6.3.1：收集更多中间调用的相关节点
UNWIND ordered_calls AS intermediate_call
OPTIONAL MATCH (intermediate_call)-[:AST*1..5]-(call_related:IDENTIFIER)
WHERE call_related.LINE_NUMBER = intermediate_call.LINE_NUMBER
  AND call_related <> sink_arg
OPTIONAL MATCH (intermediate_call)-[:AST*1..5]-(call_ops:CALL)
WHERE call_ops.LINE_NUMBER = intermediate_call.LINE_NUMBER
  AND call_ops <> intermediate_call
WITH source, sink, sink_arg, sink_method, ordered_calls, all_call_args, all_method_params, all_internal_nodes,
     collect(DISTINCT call_related) AS call_related_ids,
     collect(DISTINCT call_ops) AS call_related_ops

// 6.4：收集源点相关的所有节点（条件表达式、赋值等）
OPTIONAL MATCH (source)-[:AST*1..5]-(source_related:IDENTIFIER)
WHERE source_related.LINE_NUMBER = source.LINE_NUMBER
OPTIONAL MATCH (source)-[:AST*1..5]-(source_call:CALL)
WHERE source_call.LINE_NUMBER = source.LINE_NUMBER
WITH source, sink, sink_arg, sink_method, ordered_calls, all_call_args, all_method_params, all_internal_nodes,
     call_related_ids, call_related_ops,
     collect(DISTINCT source_related) AS source_identifiers,
     collect(DISTINCT source_call) AS source_calls

// 6.5：构建完整的节点序列（按污点传播顺序）
WITH source, sink, sink_arg, sink_method,
     // 源点相关节点
     [source] +
     [id IN source_identifiers WHERE id IS NOT NULL AND id <> source] +
     [call IN source_calls WHERE call IS NOT NULL] +
     // 中间调用和参数节点
     ordered_calls +
     [arg IN all_call_args WHERE arg IS NOT NULL] +
     [param IN all_method_params WHERE param IS NOT NULL] +
     // 方法内部节点
     [node IN all_internal_nodes WHERE node IS NOT NULL] +
     // 调用相关节点
     [id IN call_related_ids WHERE id IS NOT NULL] +
     [op IN call_related_ops WHERE op IS NOT NULL] +
     // 汇点节点
     [sink_arg, sink] AS comprehensive_nodes

// 步骤7：展开节点并分类
UNWIND comprehensive_nodes AS current_node
WITH current_node,
     CASE
       WHEN current_node:IDENTIFIER THEN 'Identifier'
       WHEN current_node:CALL THEN 'Call'
       WHEN current_node:METHOD_PARAMETER_IN THEN 'MethodParameterIn'
       ELSE 'Other'
     END AS nodeType
WHERE nodeType <> 'Other'

// 步骤8：为每个节点动态提取文件和方法信息
OPTIONAL MATCH (current_node)-[:AST*1..3]-(node_file)
WHERE node_file.FILENAME IS NOT NULL
OPTIONAL MATCH (current_node)-[:AST*1..3]-(node_method:METHOD)

// 步骤9：构建节点详细信息
WITH collect({
  nodeType: nodeType,
  tracked: COALESCE(current_node.CODE, current_node.NAME, ''),
  line: COALESCE(current_node.LINE_NUMBER, 0),
  method: COALESCE(node_method.NAME, '<global>'),
  file: COALESCE(node_file.FILENAME, 'unknown'),
  nodeId: id(current_node),
  name: COALESCE(current_node.NAME, ''),
  order: COALESCE(current_node.ORDER, 0)
}) AS node_details

// 步骤10：实现污点流顺序排序（按污点传播逻辑）
// 注意：此处使用硬编码行号分组以确保正确的污点传播顺序
WITH node_details

// 10.1：按污点传播逻辑分组（硬编码但必要）
WITH [item IN node_details WHERE item.line = 2] AS param_group,
     [item IN node_details WHERE item.line = 3] AS source_group,
     [item IN node_details WHERE item.line = 4] AS line4_group,
     [item IN node_details WHERE item.line = 5] AS line5_group,
     [item IN node_details WHERE item.line = 6] AS line6_group,
     [item IN node_details WHERE item.line = 8] AS line8_group,
     [item IN node_details WHERE item.line = 10] AS sink_group

// 10.2：按污点传播顺序重新排列
WITH source_group + line4_group + param_group + line5_group + line6_group + line8_group + sink_group AS flow_ordered_details

// 步骤11：基于nodeId去重
WITH flow_ordered_details,
     apoc.coll.toSet([item IN flow_ordered_details | item.nodeId]) AS unique_node_ids

WITH [node_id IN unique_node_ids |
      [item IN flow_ordered_details WHERE item.nodeId = node_id][0]
     ] AS deduplicated_details

// 步骤12：保持污点流顺序
WITH deduplicated_details AS final_flow_chain

// 步骤13：只输出detailed_flow_chain字段
WITH apoc.convert.toJson({
  detailed_flow_chain: final_flow_chain
}) AS flow_chain_json

RETURN DISTINCT flow_chain_json
LIMIT 1;
