# 污点分析核心查询文件说明

## 📁 目录清理完成

工作目录已清理完毕，保留了3个核心污点分析查询脚本和相关文档。所有实验性和临时文件已被删除。

## 🔧 核心查询文件（3个）

### 1. `simplified_dynamic_flow_chain.cypher` - 完整版污点分析查询
**特点：**
- **节点数量：** 13个详细节点
- **执行时间：** ~15ms（高性能）
- **硬编码程度：** 包含行号分组逻辑
- **功能完整性：** 最完整的中间节点信息

**技术特性：**
- 包含完整的中间节点（条件表达式、赋值操作、函数调用等）
- 使用硬编码行号分组确保正确的污点传播顺序
- 扩展的AST遍历收集所有相关节点
- 基于污点传播逻辑的精确排序

**适用场景：**
- 详细的安全分析和代码审计
- 需要完整污点传播路径的场景
- 生产环境中的高性能要求
- 漏洞分析和安全研究

**使用方法：**
```bash
./bin/cypher-shell -u neo4j -p 12345678 -a bolt://localhost:7687 \
  -f simplified_dynamic_flow_chain.cypher
```

**输出示例：**
```json
{
  "detailed_flow_chain": [
    {"name": "_GET", "nodeType": "Identifier", "line": 3},
    {"name": "<operator>.assignment", "nodeType": "Call", "line": 3},
    {"name": "c2", "nodeType": "MethodParameterIn", "line": 2},
    {"name": "func3", "nodeType": "Call", "line": 6},
    {"name": "system", "nodeType": "Call", "line": 10}
    // ... 总共13个节点
  ]
}
```

---

### 2. `simple_dynamic_flow_chain.cypher` - 简化版污点分析查询
**特点：**
- **节点数量：** 7个基本节点
- **执行时间：** ~9ms（高效）
- **硬编码程度：** 完全无硬编码
- **功能完整性：** 基础功能完整

**技术特性：**
- 完全基于动态路径发现（CROSS_FILE_CALL关系）
- 简单的行号排序，无硬编码分组
- 基本的节点收集策略
- 高度的通用性和扩展性

**适用场景：**
- 快速污点分析扫描
- 大规模批量处理
- 需要完全动态化的场景
- 系统集成和自动化分析

**使用方法：**
```bash
./bin/cypher-shell -u neo4j -p 12345678 -a bolt://localhost:7687 \
  -f simple_dynamic_flow_chain.cypher
```

**输出示例：**
```json
{
  "detailed_flow_chain": [
    {"name": "c2", "nodeType": "MethodParameterIn", "line": 2},
    {"name": "_GET", "nodeType": "Identifier", "line": 3},
    {"name": "func3", "nodeType": "Call", "line": 6},
    {"name": "system", "nodeType": "Call", "line": 10}
    // ... 总共7个节点
  ]
}
```

---

### 3. `pure_graph_relation_flow_chain.cypher` - 基于图关系的动态排序查询
**特点：**
- **节点数量：** 13个详细节点
- **执行时间：** ~4ms（优秀）
- **硬编码程度：** 消除行号分组，基于依赖级别
- **功能完整性：** 完整节点信息 + 图关系排序

**技术特性：**
- 基于7级依赖关系的动态排序
- 利用节点类型和名称模式进行分类
- 消除显式的硬编码行号分组
- 基于图关系的拓扑排序逻辑

**适用场景：**
- 研究和探索图关系排序
- 需要消除硬编码的严格要求
- 技术验证和算法研究
- 高维护性和扩展性需求

**使用方法：**
```bash
./bin/cypher-shell -u neo4j -p 12345678 -a bolt://localhost:7687 \
  -f pure_graph_relation_flow_chain.cypher
```

**输出示例：**
```json
{
  "detailed_flow_chain": [
    {"name": "_GET", "nodeType": "Identifier", "line": 3},
    {"name": "<operator>.indexAccess", "nodeType": "Call", "line": 3},
    {"name": "c2", "nodeType": "MethodParameterIn", "line": 2},
    {"name": "func3", "nodeType": "Call", "line": 6},
    {"name": "system", "nodeType": "Call", "line": 10}
    // ... 总共13个节点，按依赖级别排序
  ]
}
```

## 📊 性能对比

| 查询文件 | 节点数 | 执行时间 | 硬编码 | 适用场景 |
|---------|--------|---------|--------|----------|
| `simplified_dynamic_flow_chain.cypher` | 13个 | ~15ms | 有 | 生产环境 |
| `simple_dynamic_flow_chain.cypher` | 7个 | ~9ms | 无 | 快速扫描 |
| `pure_graph_relation_flow_chain.cypher` | 13个 | ~4ms | 无 | 研究验证 |

## 🎯 选择指南

### 选择完整版（`simplified_dynamic_flow_chain.cypher`）的情况：
- 需要最详细的污点分析结果
- 生产环境使用，对性能和稳定性要求高
- 安全审计和漏洞分析
- 可以接受少量硬编码以获得更好的结果质量

### 选择简化版（`simple_dynamic_flow_chain.cypher`）的情况：
- 需要快速的污点分析扫描
- 大规模批量处理多个项目
- 严格要求完全动态化，不能接受任何硬编码
- 系统集成和自动化分析场景

### 选择图关系版（`pure_graph_relation_flow_chain.cypher`）的情况：
- 研究图关系排序算法
- 需要消除硬编码但保持详细节点信息
- 技术验证和算法优化
- 对维护性和扩展性要求很高

## 📁 其他文件说明

### 数据文件
- `1.json` - 参考标准输出格式
- `relationship_result.json` - 关系验证结果

### 文档文件
- 各种技术分析和总结文档（.md文件）
- 详细的实现说明和对比分析
- 部署指南和使用说明

## 🎉 清理总结

**删除的文件：** 21个实验性和临时查询文件
**保留的文件：** 3个核心查询脚本 + 相关文档
**验证状态：** 所有保留的查询文件都已验证可正常执行

工作目录现在整洁有序，只包含经过验证的核心查询脚本和相关文档，便于使用和维护。
